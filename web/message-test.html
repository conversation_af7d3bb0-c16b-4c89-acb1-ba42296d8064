<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #2d3748;
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .message-item {
            margin-bottom: 16px;
        }
        
        .message-wrapper {
            display: flex;
            gap: 10px;
            max-width: 100%;
        }
        
        /* 自己的消息：头像在右，消息在左 */
        .message-item.is-own .message-wrapper {
            flex-direction: row; /* 保持正常顺序：消息在左，头像在右 */
        }
        
        .avatar-section {
            flex-shrink: 0;
            display: flex;
            align-items: center;
        }
        
        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: #4a5568;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .message-content {
            display: flex;
            flex-direction: column;
            max-width: calc(100% - 60px);
            min-width: 0;
        }
        
        .message-content.own-content {
            align-items: flex-start; /* 自己的消息左对齐 */
        }
        
        .message-meta {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
            font-size: 12px;
        }
        
        .message-meta.own-meta {
            justify-content: flex-start; /* 自己的消息时间左对齐 */
            margin-bottom: 4px;
            margin-top: 0;
        }
        
        .nickname {
            color: #f3f4f6;
            font-weight: 500;
        }
        
        .time {
            color: #9ca3af;
        }
        
        .message-bubble {
            position: relative;
            max-width: 100%;
            word-wrap: break-word;
            border-radius: 12px;
            padding: 10px 14px;
        }
        
        .message-bubble.own-bubble {
            background: #22c55e;
            color: white;
        }
        
        .message-bubble.own-bubble::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 12px;
            width: 0;
            height: 0;
            border-right: 6px solid #22c55e;
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
        }

        .message-bubble.other-bubble {
            background: #4b5563;
            color: #f3f4f6;
            border: 1px solid #6b7280;
        }

        .message-bubble.other-bubble::after {
            content: '';
            position: absolute;
            right: -6px;
            top: 12px;
            width: 0;
            height: 0;
            border-left: 6px solid #4b5563;
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
        }
        
        .test-controls {
            margin-bottom: 30px;
            padding: 20px;
            background: #4a5568;
            border-radius: 8px;
        }
        
        button {
            background: #3182ce;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        
        button:hover {
            background: #2c5aa0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>消息显示测试</h1>
        
        <div class="test-controls">
            <button onclick="addOtherMessage()">添加他人消息</button>
            <button onclick="addOwnMessage()">添加自己消息</button>
            <button onclick="clearMessages()">清空消息</button>
        </div>
        
        <div id="messages"></div>
    </div>

    <script>
        let messageId = 1;
        
        function createMessageHTML(message, isOwn) {
            return `
                <div class="message-item ${isOwn ? 'is-own' : ''}">
                    <div class="message-wrapper">
                        ${!isOwn ? `
                            <!-- 他人消息：头像在左，消息在右 -->
                            <div class="avatar-section">
                                <div class="message-avatar">${message.nickname[0]}</div>
                            </div>
                            <div class="message-content">
                                <div class="message-meta">
                                    <span class="nickname">${message.nickname}</span>
                                    <span class="time">${message.time}</span>
                                </div>
                                <div class="message-bubble other-bubble">
                                    <div class="bubble-content">
                                        <div class="text-content">${message.content}</div>
                                    </div>
                                </div>
                            </div>
                        ` : `
                            <!-- 自己消息：头像在右，消息在左 -->
                            <div class="message-content own-content">
                                <div class="message-meta own-meta">
                                    <span class="time">${message.time}</span>
                                </div>
                                <div class="message-bubble own-bubble">
                                    <div class="bubble-content">
                                        <div class="text-content">${message.content}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="avatar-section">
                                <div class="message-avatar">${message.nickname[0]}</div>
                            </div>
                        `}
                    </div>
                </div>
            `;
        }
        
        function addOtherMessage() {
            const message = {
                id: messageId++,
                nickname: '其他用户',
                content: '这是其他人发送的消息',
                time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
            };
            
            const messagesContainer = document.getElementById('messages');
            messagesContainer.innerHTML += createMessageHTML(message, false);
        }
        
        function addOwnMessage() {
            const message = {
                id: messageId++,
                nickname: '我',
                content: '这是我发送的消息',
                time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
            };
            
            const messagesContainer = document.getElementById('messages');
            messagesContainer.innerHTML += createMessageHTML(message, true);
        }
        
        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
            messageId = 1;
        }
        
        // 初始化一些测试消息
        addOtherMessage();
        addOwnMessage();
    </script>
</body>
</html>
