<template>
  <div class="message-panel">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <div class="header-info">
        <el-avatar :size="30" :src="conversation.avatar">
          <el-icon v-if="conversation.type === 'group'"><ChatLineRound /></el-icon>
          <el-icon v-else><User /></el-icon>
        </el-avatar>
        <div class="info-text">
          <div class="name">{{ conversation.name }}</div>
          <!-- <div class="status" v-if="conversation.type === 'friend'">
            <span :class="conversation.online ? 'online' : 'offline'">
              {{ conversation.online ? '在线' : '离线' }}
            </span>
          </div> -->
        </div>
      </div>
    </div>

    <!-- 消息列表 -->
    <div class="message-list" ref="messageListRef">
      <el-scrollbar ref="scrollbarRef" class="message-scrollbar">
        <div class="message-content">
          <div v-for="(messageGroup, date) in groupedMessages" :key="date" class="message-group">
            <div class="date-divider">{{ date }}</div>
            <div v-for="message in messageGroup" :key="message.id" class="message-item">
              <MessageItem 
                :message="message" 
                :is-own="message.isOwn"
                @avatar-click="handleAvatarClick"
              />
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <!-- 消息输入框 -->
    <div class="message-input">
      <MessageEditor @send="handleSendMessage" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, watch } from 'vue'
import MessageItem from './MessageItem.vue'
import MessageEditor from './MessageEditor.vue'

defineOptions({
  name: 'MessagePanel'
})

const props = defineProps({
  conversation: {
    type: Object,
    required: true
  }
})

const messageListRef = ref()
const scrollbarRef = ref()

// 模拟消息数据
const messages = ref([
  {
    id: 1,
    userId: 'user1',
    nickname: '张三',
    avatar: '',
    content: '222输入消息时间!',
    type: 'text',
    createdAt: new Date('2024-01-15 11:42:30'),
    isOwn: false
  },
  {
    id: 2,
    userId: 'current',
    nickname: '我',
    avatar: '',
    content: '收到',
    type: 'text',
    createdAt: new Date('2024-01-15 11:43:00'),
    isOwn: true
  },
  {
    id: 3,
    userId: 'user2',
    nickname: '李四',
    avatar: '',
    content: '大家好',
    type: 'text',
    createdAt: new Date('2024-01-15 14:50:40'),
    isOwn: false
  }
])

// 按日期分组消息
const groupedMessages = computed(() => {
  const groups = {}
  messages.value.forEach(message => {
    const date = formatDate(message.createdAt)
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(message)
  })
  return groups
})

// 格式化日期
const formatDate = (date) => {
  const now = new Date()
  const messageDate = new Date(date)
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  const messageDay = new Date(messageDate.getFullYear(), messageDate.getMonth(), messageDate.getDate())

  if (messageDay.getTime() === today.getTime()) {
    return '今天'
  } else if (messageDay.getTime() === yesterday.getTime()) {
    return '昨天'
  } else {
    return messageDate.toLocaleDateString('zh-CN', { 
      month: 'long', 
      day: 'numeric' 
    })
  }
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (scrollbarRef.value) {
      const scrollbar = scrollbarRef.value
      const wrapRef = scrollbar.wrapRef
      wrapRef.scrollTop = wrapRef.scrollHeight
    }
  })
}

// 发送消息
const handleSendMessage = (content) => {
  const newMessage = {
    id: Date.now(),
    userId: 'current',
    nickname: '我',
    avatar: '',
    content,
    type: 'text',
    createdAt: new Date(),
    isOwn: true
  }
  
  messages.value.push(newMessage)
  scrollToBottom()
}

// 头像点击
const handleAvatarClick = (message) => {
  console.log('点击头像:', message)
}

// 监听会话变化，滚动到底部
watch(() => props.conversation, () => {
  scrollToBottom()
}, { immediate: true })

// 监听消息变化，滚动到底部
watch(messages, () => {
  scrollToBottom()
}, { deep: true })
</script>

<style lang="scss" scoped>
.message-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #374151;
}

.chat-header {
  padding: 13px 20px;
  background: #4b5563;
  border-bottom: 1px solid #6b7280;

  .header-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .info-text {
      .name {
        font-size: 16px;
        font-weight: 500;
        color: #f3f4f6;
        margin-bottom: 2px;
      }

      .status {
        font-size: 12px;

        .online {
          color: #22c55e;
        }

        .offline {
          color: #9ca3af;
        }
      }
    }
  }
}

.message-list {
  flex: 1;
  overflow: hidden;
  // height: calc(750px - 120px - 80px); // 总高度 - 头部 - 输入框
  // min-height: calc(750px - 120px - 80px);
  // max-height: calc(750px - 120px - 80px);

  .message-scrollbar {
    height: 100%;
  }

  .message-content {
    padding: 20px;
  }

  .message-group {
    margin-bottom: 20px;

    .date-divider {
      text-align: center;
      color: #9ca3af;
      font-size: 12px;
      margin-bottom: 15px;
      position: relative;

      /* &::before,
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 30%;
        height: 1px;
        background: #6b7280;
      }
 */
      &::before {
        left: 0;
      }

      &::after {
        right: 0;
      }
    }

    .message-item {
      margin-bottom: 15px;
    }
  }
}

.message-input {
  background: #4b5563;
  border-top: 1px solid #6b7280;
}
</style>
