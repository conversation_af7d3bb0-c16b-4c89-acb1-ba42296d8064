<template>
  <div class="message-item" :class="{ 'is-own': isOwn }">
    <div class="message-wrapper">
      <!-- 他人消息：头像在左 -->
      <template v-if="!isOwn">
        <div class="avatar-section">
          <el-avatar
            :size="36"
            :src="message.avatar"
            @click="handleAvatarClick"
            class="message-avatar"
          >
            <el-icon><User /></el-icon>
          </el-avatar>
        </div>

        <div class="message-content">
          <div class="message-meta">
            <span class="nickname">{{ message.nickname }}</span>
            <span class="time">{{ formatTime(message.createdAt) }}</span>
          </div>

          <div class="message-bubble" :class="getBubbleClass()">
            <div class="bubble-content">
              <div v-if="message.type === 'text'" class="text-content">
                {{ message.content }}
              </div>

              <div v-else-if="message.type === 'image'" class="image-content">
                <el-image
                  :src="message.content"
                  fit="cover"
                  class="message-image"
                  :preview-src-list="[message.content]"
                />
              </div>

              <div v-else-if="message.type === 'file'" class="file-content">
                <el-icon class="file-icon"><Document /></el-icon>
                <span class="file-name">{{ message.fileName || '文件' }}</span>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 自己消息：头像在右 -->
      <template v-else>
        <div class="message-content own-content">
          <div class="message-bubble" :class="getBubbleClass()">
            <div class="bubble-content">
              <div v-if="message.type === 'text'" class="text-content">
                {{ message.content }}
              </div>

              <div v-else-if="message.type === 'image'" class="image-content">
                <el-image
                  :src="message.content"
                  fit="cover"
                  class="message-image"
                  :preview-src-list="[message.content]"
                />
              </div>

              <div v-else-if="message.type === 'file'" class="file-content">
                <el-icon class="file-icon"><Document /></el-icon>
                <span class="file-name">{{ message.fileName || '文件' }}</span>
              </div>
            </div>

            <!-- <div class="message-status">
              <MessageStatus :status="message.status" />
            </div> -->
          </div>

          <div class="message-meta own-meta">
            <span class="time">{{ formatTime(message.createdAt) }}</span>
          </div>
        </div>

        <div class="avatar-section">
          <el-avatar
            :size="36"
            :src="message.avatar"
            class="message-avatar"
          >
            <el-icon><User /></el-icon>
          </el-avatar>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { User, Document } from '@element-plus/icons-vue'

defineOptions({
  name: 'MessageItem'
})

const props = defineProps({
  message: {
    type: Object,
    required: true
  },
  isOwn: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['avatar-click'])

// 格式化时间
const formatTime = (date) => {
  return new Date(date).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取气泡样式类
const getBubbleClass = () => {
  return {
    'own-bubble': props.isOwn,
    'other-bubble': !props.isOwn,
    [`${props.message.type}-bubble`]: true
  }
}

// 头像点击事件
const handleAvatarClick = () => {
  emit('avatar-click', props.message)
}
</script>

<style lang="scss" scoped>
.message-item {
  margin-bottom: 16px;
}

.message-wrapper {
  display: flex;
  gap: 10px;
  max-width: 100%;
}

// 自己的消息：右对齐
.message-item.is-own {
  .message-wrapper {
    flex-direction: row-reverse;
  }
}

.avatar-section {
  flex-shrink: 0;
  display: flex;
  align-items: center;

  .message-avatar {
    cursor: pointer;
    transition: transform 0.2s;

    &:hover {
      transform: scale(1.05);
    }
  }
}

.message-content {
  display: flex;
  flex-direction: column;
  max-width: calc(100% - 60px);
  min-width: 0;

  &.own-content {
    align-items: flex-end;
  }
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 12px;

  .nickname {
    color: #f3f4f6;
    font-weight: 500;
  }

  .time {
    color: #9ca3af;
  }

  &.own-meta {
    justify-content: flex-end;
    margin-bottom: 0;
    margin-top: 4px;
  }
}

.message-bubble {
  position: relative;
  max-width: 100%;
  word-wrap: break-word;
  border-radius: 12px;
  padding: 10px 14px;

  &.own-bubble {
    background: #22c55e;
    color: white;

    &::after {
      content: '';
      position: absolute;
      right: -6px;
      top: 12px;
      width: 0;
      height: 0;
      border-left: 6px solid #22c55e;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
    }
  }

  &.other-bubble {
    background: #4b5563;
    color: #f3f4f6;
    border: 1px solid #6b7280;

    &::before {
      content: '';
      position: absolute;
      left: -6px;
      top: 12px;
      width: 0;
      height: 0;
      border-right: 6px solid #4b5563;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
    }
  }

  .bubble-content {
    .text-content {
      line-height: 1.4;
    }

    .image-content {
      .message-image {
        max-width: 200px;
        max-height: 200px;
        border-radius: 8px;
        cursor: pointer;
      }
    }

    .file-content {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px;

      .file-icon {
        font-size: 20px;
        color: #60a5fa;
      }

      .file-name {
        font-size: 14px;
      }
    }
  }

  .message-status {
    margin-top: 4px;
    text-align: right;
  }
}
</style>
